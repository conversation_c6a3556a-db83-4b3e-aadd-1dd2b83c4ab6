package com.hmall;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@MapperScan("com.hmall.mapper")
@SpringBootApplication
public class HMallApplication {
    public static void main(String[] args) {
        // 方案二：通过代码设置默认profile（如果需要的话可以取消注释）
        // SpringApplication app = new SpringApplication(HMallApplication.class);
        // app.setDefaultProperties(Collections.singletonMap("spring.profiles.active", "local"));
        // app.run(args);

        // 当前使用方案一：通过application.yaml配置文件设置
        SpringApplication.run(HMallApplication.class, args);
    }
}